com\example\entity\IdentityVerification.class
com\example\entity\Menu.class
com\example\entity\Order.class
com\example\entity\User.class
com\example\dto\FavoriteItemDto.class
com\example\repository\IdentityVerificationRepository.class
com\example\Main.class
com\example\config\JpaConfig.class
com\example\repository\ProductRepository.class
com\example\service\FavoriteCacheService.class
com\example\controller\AdminAuthController.class
com\example\config\ThreadPoolConfig.class
com\example\controller\IdentityController.class
com\example\controller\MenuController.class
com\example\controller\PaymentController.class
com\example\dto\PagedResponse.class
com\example\config\RedisConfig.class
com\example\SimpleEmailTest.class
com\example\service\FavoriteService.class
com\example\service\ProductCategoryService.class
com\example\dto\MenuCreateRequest.class
com\example\entity\Product$HotFlag.class
com\example\repository\CartItemRepository.class
com\example\controller\AdminIdentityController.class
com\example\controller\CartController.class
com\example\repository\ProductImageRepository.class
com\example\entity\IdentityVerification$Status.class
com\example\entity\ProductCategory.class
com\example\repository\ProductCategoryRepository.class
com\example\dto\MenuUpdateRequest.class
com\example\repository\CartRepository.class
com\example\service\RefreshTokenService.class
com\example\dto\LoginRequest.class
com\example\entity\UserFollow.class
com\example\entity\FavoriteItem.class
com\example\entity\Cart.class
com\example\util\ProductRedisUtil$ExpireTime.class
com\example\dto\FavoriteStatsDto.class
com\example\entity\Menu$MenuType.class
com\example\entity\OrderItem.class
com\example\entity\Payment.class
com\example\entity\ProductCategory$LeafFlag.class
com\example\service\EmailService$Type.class
com\example\exception\DuplicateFavoriteException.class
com\example\config\SecurityConfig.class
com\example\entity\Payment$PaymentStatus.class
com\example\service\AdminService.class
com\example\dto\MenuDto.class
com\example\SimpleTestController.class
com\example\enums\ItemType.class
com\example\dto\RegisterRequest.class
com\example\service\UserFollowService.class
com\example\repository\AdminRepository.class
com\example\repository\OrderItemRepository.class
com\example\dto\OrderCreateRequest.class
com\example\controller\AuthController.class
com\example\controller\FavoriteController.class
com\example\entity\User$IdentityStatus.class
com\example\exception\FavoriteException.class
com\example\service\PaymentService.class
com\example\util\ProductRedisUtil.class
com\example\controller\UserFollowController.class
com\example\repository\FavoriteItemRepository.class
com\example\entity\CartItem.class
com\example\entity\ProductImage$MainFlag.class
com\example\exception\FavoriteExceptionHandler.class
com\example\service\MenuService.class
com\example\dto\FavoriteDto.class
com\example\entity\Admin.class
com\example\entity\Product.class
com\example\entity\ProductImage.class
com\example\exception\UnauthorizedFavoriteException.class
com\example\service\impl\FavoriteServiceImpl.class
com\example\entity\Product$RecommendFlag.class
com\example\dto\ApiResponse.class
com\example\service\FileService.class
com\example\controller\ProductController.class
com\example\service\RedisService.class
com\example\entity\Product$Status.class
com\example\dto\EmailVerificationRequest.class
com\example\exception\RateLimitExceededException.class
com\example\entity\Favorite.class
com\example\repository\MenuRepository.class
com\example\controller\TestController.class
com\example\service\CartService.class
com\example\controller\OrderController.class
com\example\repository\UserFollowRepository.class
com\example\config\FavoriteCacheConfig.class
com\example\entity\Cart$Status.class
com\example\repository\PaymentRepository.class
com\example\config\CompositeAuthenticationProvider.class
com\example\controller\ProductCategoryController.class
com\example\dto\RefreshTokenRequest.class
com\example\service\UserService.class
com\example\entity\CartItem$Selected.class
com\example\entity\User$Role.class
com\example\util\JwtUtil.class
com\example\config\JwtAuthenticationFilter.class
com\example\service\IdentityVerificationService.class
com\example\dto\ProductCategoryDTO.class
com\example\service\EmailService.class
com\example\controller\UserController.class
com\example\util\ProductRedisKeyUtil.class
com\example\exception\FavoriteNotFoundException.class
com\example\entity\Admin$Role.class
com\example\entity\Payment$PaymentMethod.class
com\example\entity\ProductCategory$Status.class
com\example\config\MenuCacheInitializer.class
com\example\service\impl\ProductCategoryServiceImpl.class
com\example\service\ProductService.class
com\example\repository\FavoriteRepository.class
com\example\repository\OrderRepository.class
com\example\service\impl\ProductServiceImpl.class
com\example\entity\Order$Status.class
com\example\dto\IdentityVerificationRequest.class
com\example\repository\UserRepository.class
com\example\controller\AdminTestController.class
com\example\controller\FavoriteTestController.class
com\example\controller\FileController.class
com\example\service\OrderService.class
