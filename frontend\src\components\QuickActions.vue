<template>
  <div class="quick-actions">
    <!-- 快捷操作按鈕 -->
    <div class="action-buttons">
      <el-tooltip content="返回頂部" placement="left">
        <el-button 
          v-show="showBackToTop"
          type="primary" 
          :icon="ArrowUp"
          circle
          size="large"
          class="back-to-top"
          @click="scrollToTop"
        />
      </el-tooltip>

      <el-tooltip content="購物車" placement="left">
        <el-button 
          type="primary" 
          :icon="ShoppingCart"
          circle
          size="large"
          class="cart-button"
          @click="goToCart"
        >
          <el-badge :value="cartCount" :hidden="cartCount === 0" />
        </el-button>
      </el-tooltip>

      <el-tooltip content="我的收藏" placement="left">
        <el-button 
          type="primary" 
          :icon="Star"
          circle
          size="large"
          class="favorites-button"
          @click="goToFavorites"
        />
      </el-tooltip>

      <el-tooltip content="客服諮詢" placement="left">
        <el-button 
          type="primary" 
          :icon="Service"
          circle
          size="large"
          class="service-button"
          @click="openService"
        />
      </el-tooltip>
    </div>

    <!-- 快捷搜索 -->
    <transition name="search-slide">
      <div v-if="showQuickSearch" class="quick-search">
        <el-input
          v-model="searchKeyword"
          placeholder="快速搜索商品..."
          :prefix-icon="Search"
          size="large"
          @keyup.enter="performSearch"
          @blur="hideQuickSearch"
          ref="searchInputRef"
        >
          <template #append>
            <el-button :icon="Search" @click="performSearch" />
          </template>
        </el-input>
      </div>
    </transition>

    <!-- 搜索觸發按鈕 -->
    <el-tooltip content="快速搜索" placement="left">
      <el-button 
        type="primary" 
        :icon="Search"
        circle
        size="large"
        class="search-trigger"
        @click="toggleQuickSearch"
      />
    </el-tooltip>

    <!-- 快捷鍵提示 -->
    <div v-if="showKeyboardShortcuts" class="keyboard-shortcuts">
      <div class="shortcuts-header">
        <h4>快捷鍵</h4>
        <el-button type="text" @click="hideKeyboardShortcuts">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      <div class="shortcuts-list">
        <div class="shortcut-item">
          <kbd>Ctrl</kbd> + <kbd>K</kbd> - 快速搜索
        </div>
        <div class="shortcut-item">
          <kbd>Ctrl</kbd> + <kbd>B</kbd> - 購物車
        </div>
        <div class="shortcut-item">
          <kbd>Ctrl</kbd> + <kbd>H</kbd> - 返回首頁
        </div>
        <div class="shortcut-item">
          <kbd>Esc</kbd> - 關閉彈窗
        </div>
      </div>
    </div>

    <!-- 快捷鍵觸發按鈕 -->
    <el-tooltip content="快捷鍵說明" placement="left">
      <el-button 
        type="primary" 
        :icon="Keyboard"
        circle
        size="large"
        class="keyboard-button"
        @click="toggleKeyboardShortcuts"
      />
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowUp, ShoppingCart, Star, Service, Search, 
  Close, Keyboard 
} from '@element-plus/icons-vue'

// Props
interface Props {
  cartCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  cartCount: 0
})

// 響應式數據
const router = useRouter()
const showBackToTop = ref(false)
const showQuickSearch = ref(false)
const showKeyboardShortcuts = ref(false)
const searchKeyword = ref('')
const searchInputRef = ref()

// 方法
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

const goToCart = () => {
  router.push('/cart')
}

const goToFavorites = () => {
  router.push('/favorites')
}

const openService = () => {
  ElMessage.info('客服功能開發中，敬請期待')
}

const toggleQuickSearch = async () => {
  showQuickSearch.value = !showQuickSearch.value
  if (showQuickSearch.value) {
    await nextTick()
    searchInputRef.value?.focus()
  }
}

const hideQuickSearch = () => {
  setTimeout(() => {
    showQuickSearch.value = false
  }, 200)
}

const performSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push(`/products?search=${encodeURIComponent(searchKeyword.value)}`)
    showQuickSearch.value = false
    searchKeyword.value = ''
  }
}

const toggleKeyboardShortcuts = () => {
  showKeyboardShortcuts.value = !showKeyboardShortcuts.value
}

const hideKeyboardShortcuts = () => {
  showKeyboardShortcuts.value = false
}

// 滾動監聽
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

// 鍵盤快捷鍵
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl + K: 快速搜索
  if (event.ctrlKey && event.key === 'k') {
    event.preventDefault()
    toggleQuickSearch()
  }
  
  // Ctrl + B: 購物車
  if (event.ctrlKey && event.key === 'b') {
    event.preventDefault()
    goToCart()
  }
  
  // Ctrl + H: 返回首頁
  if (event.ctrlKey && event.key === 'h') {
    event.preventDefault()
    router.push('/')
  }
  
  // Esc: 關閉彈窗
  if (event.key === 'Escape') {
    showQuickSearch.value = false
    showKeyboardShortcuts.value = false
  }
}

// 生命週期
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  window.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.quick-actions {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.back-to-top,
.cart-button,
.favorites-button,
.service-button,
.search-trigger,
.keyboard-button {
  width: 50px;
  height: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.back-to-top:hover,
.cart-button:hover,
.favorites-button:hover,
.service-button:hover,
.search-trigger:hover,
.keyboard-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.cart-button {
  position: relative;
}

.quick-search {
  position: absolute;
  right: 0;
  bottom: 70px;
  width: 300px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px;
}

.keyboard-shortcuts {
  position: absolute;
  right: 0;
  bottom: 70px;
  width: 250px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
}

.shortcuts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.shortcuts-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.shortcut-item {
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

kbd {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 12px;
  font-family: monospace;
}

/* 動畫 */
.search-slide-enter-active,
.search-slide-leave-active {
  transition: all 0.3s ease;
}

.search-slide-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.search-slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .quick-actions {
    right: 15px;
    bottom: 15px;
  }
  
  .back-to-top,
  .cart-button,
  .favorites-button,
  .service-button,
  .search-trigger,
  .keyboard-button {
    width: 45px;
    height: 45px;
  }
  
  .quick-search {
    width: 250px;
  }
  
  .keyboard-shortcuts {
    width: 200px;
  }
}
</style>
