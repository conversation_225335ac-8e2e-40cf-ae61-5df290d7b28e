<template>
  <header class="app-header">
    <div class="header-content">
      <div class="brand">
        <el-icon class="logo-icon"><Monitor /></el-icon>
        <h1>用戶認證系統</h1>
      </div>
      <div class="user-menu" v-if="userStore.isLoggedIn">
        <el-dropdown @command="handleCommand" trigger="click">
          <div class="user-profile">
            <div class="avatar">
              <el-avatar :size="40" :icon="User" />
            </div>
            <div class="user-info">
              <span class="username">{{ userStore.user?.username }}</span>
              <span class="role">{{ userStore.user?.role === 'ADMIN' ? '管理員' : '普通用戶' }}</span>
            </div>
            <el-icon><arrow-down /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                <span>個人資料</span>
              </el-dropdown-item>
              <el-dropdown-item command="identity">
                <el-icon><CreditCard /></el-icon>
                <span>身份認證</span>
              </el-dropdown-item>
              <el-dropdown-item command="follow">
                <el-icon><UserFilled /></el-icon>
                <span>社交關注</span>
              </el-dropdown-item>
              <el-dropdown-item command="my-favorites">
                <el-icon><Star /></el-icon>
                <span>我的收藏</span>
              </el-dropdown-item>
              <el-dropdown-item command="favorite-ranking">
                <el-icon><Trophy /></el-icon>
                <span>收藏排行榜</span>
              </el-dropdown-item>
              <el-dropdown-item command="products">
                <el-icon><ShoppingBag /></el-icon>
                <span>商品列表</span>
              </el-dropdown-item>
              <el-dropdown-item v-if="userStore.isAdmin" command="admin">
                <el-icon><Setting /></el-icon>
                <span>管理後台</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><Switch /></el-icon>
                <span>登出</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowDown,
  User,
  CreditCard,
  UserFilled,
  Setting,
  Switch,
  Monitor,
  Star,
  Trophy,
  ShoppingBag
} from '@element-plus/icons-vue'
import { useUserStore } from '../../stores/user'

const router = useRouter()
const userStore = useUserStore()

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/app/profile')
      break
    case 'identity':
      router.push('/app/identity')
      break
    case 'follow':
      router.push('/app/follow')
      break
    case 'my-favorites':
      router.push('/app/my-favorites')
      break
    case 'favorite-ranking':
      router.push('/favorite-ranking')
      break
    case 'products':
      router.push('/products')
      break
    case 'admin':
      router.push('/admin')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('確定要登出嗎？', '提示', {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    userStore.logout()
    ElMessage.success('已登出')
    router.push('/login')
  } catch {
    // 用戶取消
  }
}
</script>

<style scoped>
.app-header {
  background: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
  color: white;
  height: 70px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 30px;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
}

.brand {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.logo-icon {
  font-size: 24px;
  margin-right: 12px;
}

.header-content h1 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
}

.user-menu {
  position: relative;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 30px;
  background: rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
}

.user-profile:hover {
  background: rgba(255, 255, 255, 0.25);
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.username {
  font-weight: 600;
  font-size: 14px;
}

.role {
  font-size: 12px;
  opacity: 0.8;
}

/* 響應式調整 */
@media (max-width: 992px) {
  .header-content {
    padding: 0 20px;
  }
}

@media (max-width: 768px) {
  .role {
    display: none;
  }
}

@media (max-width: 576px) {
  .app-header {
    height: 60px;
  }
  
  .header-content h1 {
    font-size: 18px;
  }
}

/* 針對大型螢幕 (2560x1440) 的優化 */
@media (min-width: 1920px) {
  .header-content {
    max-width: 1800px;
  }
}
</style> 