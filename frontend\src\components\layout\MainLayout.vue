<template>
  <div class="main-layout">
    <!-- 側邊欄 -->
    <aside class="sidebar" :class="{ collapsed: menuStore.collapsed }">
      <div class="sidebar-header">
        <div class="brand" v-if="!menuStore.collapsed">
          <el-icon class="logo-icon"><Monitor /></el-icon>
          <h2>用戶認證系統</h2>
        </div>
        <div class="brand-collapsed" v-else>
          <el-icon class="logo-icon"><Monitor /></el-icon>
        </div>
      </div>

      <div class="sidebar-content">
        <MenuTree
          ref="menuTreeRef"
          :collapsed="menuStore.collapsed"
          @menu-select="handleMenuSelect"
          @load-complete="handleMenuLoadComplete"
          @load-error="handleMenuLoadError"
        />
      </div>

      <div class="sidebar-footer">
        <el-button
          :icon="menuStore.collapsed ? Expand : Fold"
          @click="toggleSidebar"
          class="collapse-btn"
          text
        />
      </div>
    </aside>

    <!-- 主內容區域 -->
    <div class="main-content">
      <!-- 頂部導航 -->
      <header class="main-header">
        <div class="header-left">
          <!-- 面包屑導航 -->
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/app/home' }">
              <el-icon><House /></el-icon>
              首頁
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="currentPageName">
              <el-icon><component :is="currentPageIcon" /></el-icon>
              {{ currentPageName }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 刷新菜單按鈕（管理員可見） -->
          <el-button
            v-if="userStore.isAdmin"
            :icon="Refresh"
            @click="refreshMenu"
            :loading="menuStore.isLoading"
            text
            title="刷新菜單"
          />

          <!-- 用戶下拉菜單 -->
          <el-dropdown @command="handleCommand" trigger="click">
            <div class="user-profile">
              <div class="avatar">
                <el-avatar :size="32" :icon="User" />
              </div>
              <div class="user-info" v-if="!menuStore.collapsed">
                <span class="username">{{ userStore.user?.username }}</span>
                <span class="role">{{ userStore.user?.role === 'ADMIN' ? '管理員' : '普通用戶' }}</span>
              </div>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  <span>個人資料</span>
                </el-dropdown-item>
                <el-dropdown-item command="identity">
                  <el-icon><CreditCard /></el-icon>
                  <span>身份認證</span>
                </el-dropdown-item>
                <el-dropdown-item command="follow">
                  <el-icon><UserFilled /></el-icon>
                  <span>社交關注</span>
                </el-dropdown-item>
                <el-dropdown-item v-if="userStore.isAdmin" command="admin">
                  <el-icon><Setting /></el-icon>
                  <span>管理後台</span>
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><Switch /></el-icon>
                  <span>登出</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 頁面內容 -->
      <main class="page-content">
        <router-view></router-view>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Monitor,
  Expand,
  Fold,
  Refresh,
  User,
  CreditCard,
  UserFilled,
  Setting,
  Switch,
  ArrowDown,
  House,
  Document,
  Menu as MenuIcon,
  Star
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { useMenuStore } from '@/stores/menu'
import MenuTree from '@/components/menu/MenuTree.vue'
import type { MenuDto } from '@/api/menu'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const menuStore = useMenuStore()
const menuTreeRef = ref()

// 圖標組件映射
const iconComponents = {
  House,
  User,
  UserFilled,
  Setting,
  Menu: MenuIcon,
  Document,
  Monitor
}

const getIconComponent = (iconName: string) => {
  return iconComponents[iconName as keyof typeof iconComponents] || Document
}

// 计算当前页面信息
const currentPageName = computed(() => {
  const path = route.path
  const pageMap: Record<string, string> = {
    '/app/home': '',
    '/app/profile': '個人資料',
    '/app/identity': '身份認證',
    '/app/follow': '社交關注',
    '/app/my-favorites': '我的收藏'
  }
  return pageMap[path] || ''
})

const currentPageIcon = computed(() => {
  const path = route.path
  const iconMap: Record<string, any> = {
    '/app/home': House,
    '/app/profile': User,
    '/app/identity': CreditCard,
    '/app/follow': UserFilled,
    '/app/my-favorites': Star
  }
  return iconMap[path] || Document
})

// 監聽路由變化，更新面包屑
watch(() => route.path, (newPath) => {
  // 暂时禁用 menuStore 的路由监听，避免错误
  // menuStore.setActiveMenu(newPath)
}, { immediate: true })

// 方法
const toggleSidebar = () => {
  menuStore.toggleCollapsed()
}

const handleMenuSelect = (menu: MenuDto) => {
  console.log('菜單選中:', menu)
}

const handleMenuLoadComplete = (menuTree: MenuDto[]) => {
  console.log('菜單加載完成:', menuTree)
}

const handleMenuLoadError = (error: string) => {
  ElMessage.error(error)
}

const refreshMenu = async () => {
  try {
    await menuStore.refreshMenuTree()
    ElMessage.success('菜單刷新成功')
  } catch (error: any) {
    ElMessage.error(error.message || '菜單刷新失敗')
  }
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/app/profile')
      break
    case 'identity':
      router.push('/app/identity')
      break
    case 'follow':
      router.push('/app/follow')
      break
    case 'admin':
      router.push('/admin')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('確定要登出嗎？', '確認', {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await userStore.logout()
    menuStore.clearMenuData()

    ElMessage.success('登出成功')
    router.push('/login')

  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '登出失敗')
    }
  }
}

// 生命週期
onMounted(() => {
  // 启用动态菜单加载
  menuStore.loadMenuTree()
})
</script>

<style scoped>
.main-layout {
  display: flex;
  height: 100vh;
  background-color: #f0f2f5;
}

/* 側邊欄樣式 */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  flex-direction: column;
  transition: width 0.2s ease;
  overflow: hidden;
  box-shadow: 2px 0 8px rgba(102, 126, 234, 0.1);
  position: relative;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 20px;
}

.brand {
  display: flex;
  align-items: center;
  gap: 10px;
  transition: opacity 0.2s ease;
}

.brand h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.brand-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon {
  font-size: 32px;
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  position: relative;
  z-index: 1;
}

.sidebar-footer {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 1;
}

.collapse-btn {
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  transition: background-color 0.15s ease;
  background: rgba(255, 255, 255, 0.1);
}

.collapse-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 主內容區域樣式 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.main-header {
  height: 64px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-left {
  flex: 1;
}

.breadcrumb {
  font-size: 15px;
  font-weight: 500;
}

.breadcrumb .el-icon {
  margin-right: 6px;
  color: #667eea;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 10px 14px;
  border-radius: 12px;
  transition: background-color 0.2s ease;
  background: rgba(102, 126, 234, 0.08);
  border: 1px solid rgba(102, 126, 234, 0.15);
}

.user-profile:hover {
  background: rgba(102, 126, 234, 0.15);
}

.avatar {
  position: relative;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.username {
  font-size: 15px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 2px;
}

.role {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
}

.page-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background: transparent;
}

/* 自定義滾動條 */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.page-content::-webkit-scrollbar {
  width: 8px;
}

.page-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
}

.page-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.page-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar {
    width: 260px;
  }
  
  .sidebar.collapsed {
    width: 70px;
  }
  
  .main-header {
    padding: 0 24px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .main-content {
    width: 100%;
  }
  
  .main-header {
    padding: 0 16px;
  }
  
  .user-profile {
    padding: 8px 12px;
    gap: 8px;
  }
  
  .username {
    font-size: 14px;
  }
}
</style>