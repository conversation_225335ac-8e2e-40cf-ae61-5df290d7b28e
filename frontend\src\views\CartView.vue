<template>
  <div class="cart-view">
    <div class="container">
      <h1 class="page-title">購物車</h1>
      
      <!-- 購物車為空 -->
      <div v-if="cartItems.length === 0" class="empty-cart">
        <div class="empty-icon">🛒</div>
        <h3>您的購物車還是空的</h3>
        <p>快去挑選您喜歡的商品吧！</p>
        <div class="empty-actions">
          <router-link to="/products" class="btn btn-primary">
            <el-icon><ShoppingBag /></el-icon>
            去購物
          </router-link>
          <router-link to="/products/hot" class="btn btn-outline">
            <el-icon><Fire /></el-icon>
            熱門商品
          </router-link>
        </div>

        <!-- 推薦商品 -->
        <div class="recommended-products" v-if="recommendedProducts.length > 0">
          <h4>為您推薦</h4>
          <div class="recommended-grid">
            <el-card
              v-for="product in recommendedProducts"
              :key="product.id"
              class="recommended-card"
              shadow="hover"
              @click="goToProduct(product.id)"
            >
              <img :src="product.mainImageUrl" :alt="product.name" class="recommended-image" />
              <div class="recommended-info">
                <h5>{{ product.name }}</h5>
                <div class="recommended-price">¥{{ product.price.toFixed(2) }}</div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
      
      <!-- 購物車商品列表 -->
      <div v-else class="cart-content">
        <div class="cart-header">
          <div class="header-left">
            <el-checkbox
              v-model="selectAll"
              @change="toggleSelectAll"
              size="large"
            >
              全選
            </el-checkbox>
            <span class="item-count">共 {{ cartItems.length }} 件商品</span>
          </div>
          <div class="header-right">
            <el-button
              type="text"
              @click="clearInvalidItems"
              v-if="invalidItems.length > 0"
            >
              <el-icon><Delete /></el-icon>
              清理失效商品 ({{ invalidItems.length }})
            </el-button>
            <el-button
              type="text"
              @click="refreshCart"
              :loading="loading"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
        
        <div class="cart-items">
          <div
            v-for="item in cartItems"
            :key="item.id"
            class="cart-item"
            :class="{
              selected: item.selected,
              invalid: item.invalid,
              'low-stock': item.stock < item.quantity
            }"
          >
            <div class="item-checkbox">
              <el-checkbox
                v-model="item.selected"
                @change="toggleItemSelected(item)"
                :disabled="item.invalid"
                size="large"
              />
            </div>

            <div class="item-image" @click="goToProduct(item.productId)">
              <img :src="item.productImageUrl || '/default-product.jpg'" :alt="item.productName" />
              <div v-if="item.invalid" class="invalid-overlay">
                <span>商品失效</span>
              </div>
            </div>

            <div class="item-info">
              <h3 class="item-name" @click="goToProduct(item.productId)">
                {{ item.productName }}
              </h3>
              <div class="item-specs" v-if="item.specs">
                <span v-for="(value, key) in item.specs" :key="key" class="spec-tag">
                  {{ key }}: {{ value }}
                </span>
              </div>
              <div class="item-price">
                <span class="current-price">¥{{ item.price.toFixed(2) }}</span>
                <span v-if="item.originalPrice && item.originalPrice > item.price" class="original-price">
                  ¥{{ item.originalPrice.toFixed(2) }}
                </span>
              </div>
              <div v-if="item.stock < item.quantity" class="stock-warning">
                <el-icon><WarningFilled /></el-icon>
                庫存不足，僅剩 {{ item.stock }} 件
              </div>
            </div>

            <div class="item-quantity">
              <div class="quantity-label">數量</div>
              <div class="quantity-controls">
                <el-button
                  :icon="Minus"
                  size="small"
                  @click="updateQuantity(item, item.quantity - 1)"
                  :disabled="item.quantity <= 1 || item.invalid"
                />
                <el-input-number
                  v-model="item.quantity"
                  @change="updateQuantity(item, item.quantity)"
                  :min="1"
                  :max="item.stock || 999"
                  size="small"
                  :disabled="item.invalid"
                  controls-position="right"
                />
                <el-button
                  :icon="Plus"
                  size="small"
                  @click="updateQuantity(item, item.quantity + 1)"
                  :disabled="item.quantity >= (item.stock || 999) || item.invalid"
                />
              </div>
            </div>

            <div class="item-subtotal">
              <div class="subtotal-label">小計</div>
              <div class="subtotal-price">
                ¥{{ (item.price * item.quantity).toFixed(2) }}
              </div>
            </div>

            <div class="item-actions">
              <el-dropdown trigger="click">
                <el-button type="text" class="action-btn">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="moveToFavorites(item)">
                      <el-icon><Star /></el-icon>
                      移入收藏夾
                    </el-dropdown-item>
                    <el-dropdown-item @click="findSimilar(item)">
                      <el-icon><Search /></el-icon>
                      找相似
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="removeItem(item)">
                      <el-icon><Delete /></el-icon>
                      刪除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
        
        <!-- 購物車底部 -->
        <div class="cart-footer">
          <div class="footer-left">
            <el-checkbox
              v-model="selectAll"
              @change="toggleSelectAll"
              size="large"
            >
              全選
            </el-checkbox>
            <el-button
              type="text"
              @click="clearCart"
              :disabled="cartItems.length === 0"
            >
              清空購物車
            </el-button>
          </div>

          <div class="footer-right">
            <div class="cart-summary">
              <div class="summary-line">
                <span class="selected-count">
                  已選擇 {{ selectedItems.length }} 件商品
                </span>
                <span class="item-total">
                  商品總價：¥{{ itemTotal.toFixed(2) }}
                </span>
              </div>
              <div class="summary-line" v-if="discount > 0">
                <span class="discount-info">
                  優惠減免：-¥{{ discount.toFixed(2) }}
                </span>
              </div>
              <div class="summary-line total-line">
                <span class="total-label">合計：</span>
                <span class="total-amount">¥{{ totalAmount.toFixed(2) }}</span>
              </div>
            </div>

            <div class="cart-actions">
              <el-button
                type="primary"
                size="large"
                @click="goToCheckout"
                :disabled="selectedItems.length === 0 || hasInvalidItems"
                class="checkout-btn"
              >
                <el-icon><ShoppingCartFull /></el-icon>
                去結算 ({{ selectedItems.length }})
              </el-button>
            </div>
          </div>
        </div>

        <!-- 購物車推薦商品 -->
        <div class="cart-recommendations" v-if="recommendedProducts.length > 0">
          <div class="recommendations-header">
            <h3>
              <el-icon><Star /></el-icon>
              為您推薦
            </h3>
            <el-button type="text" @click="refreshRecommendations">
              <el-icon><Refresh /></el-icon>
              換一批
            </el-button>
          </div>
          <div class="recommendations-grid">
            <el-card
              v-for="product in recommendedProducts"
              :key="product.id"
              class="recommendation-card"
              shadow="hover"
            >
              <div class="recommendation-image" @click="goToProduct(product.id)">
                <img :src="product.mainImageUrl" :alt="product.name" />
              </div>
              <div class="recommendation-info">
                <h4 @click="goToProduct(product.id)">{{ product.name }}</h4>
                <div class="recommendation-price">¥{{ product.price.toFixed(2) }}</div>
                <el-button
                  type="primary"
                  size="small"
                  @click="addRecommendedToCart(product)"
                  class="add-btn"
                >
                  <el-icon><Plus /></el-icon>
                  加入購物車
                </el-button>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ShoppingBag, Fire, Delete, Refresh, WarningFilled,
  Minus, Plus, MoreFilled, Star, Search, ShoppingCartFull
} from '@element-plus/icons-vue'

interface CartItem {
  id: number
  productId: number
  productName: string
  productImageUrl: string
  price: number
  originalPrice?: number
  quantity: number
  selected: boolean
  invalid?: boolean
  stock?: number
  specs?: Record<string, string>
}

interface Product {
  id: number
  name: string
  price: number
  mainImageUrl: string
}

const router = useRouter()
const cartItems = ref<CartItem[]>([])
const recommendedProducts = ref<Product[]>([])
const loading = ref(false)

// 計算屬性
const selectedItems = computed(() =>
  cartItems.value.filter(item => item.selected && !item.invalid)
)

const invalidItems = computed(() =>
  cartItems.value.filter(item => item.invalid)
)

const itemTotal = computed(() =>
  selectedItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
)

const discount = computed(() => {
  // 這裡可以添加優惠計算邏輯
  return 0
})

const totalAmount = computed(() =>
  Math.max(0, itemTotal.value - discount.value)
)

const hasInvalidItems = computed(() =>
  selectedItems.value.some(item => item.invalid || (item.stock && item.stock < item.quantity))
)

const selectAll = computed({
  get: () => {
    const validItems = cartItems.value.filter(item => !item.invalid)
    return validItems.length > 0 && validItems.every(item => item.selected)
  },
  set: (value: boolean) => {
    cartItems.value.forEach(item => {
      if (!item.invalid) {
        item.selected = value
      }
    })
  }
})

// 方法
const loadCart = async () => {
  try {
    loading.value = true
    const response = await fetch('/api/cart', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data.cartItems) {
        cartItems.value = result.data.cartItems.map((item: any) => ({
          ...item,
          selected: item.selected === 1,
          invalid: item.stock === 0 || !item.productId, // 檢查商品是否失效
          stock: item.stock || 0
        }))
      }
    }
  } catch (error) {
    console.error('加載購物車失敗:', error)
    ElMessage.error('加載購物車失敗')
  } finally {
    loading.value = false
  }
}

// 加載推薦商品
const loadRecommendedProducts = async () => {
  try {
    const response = await fetch('/api/products?page=0&size=6', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data.content) {
        recommendedProducts.value = result.data.content.slice(0, 6)
      }
    }
  } catch (error) {
    console.warn('加載推薦商品失敗:', error)
  }
}

// 刷新購物車
const refreshCart = async () => {
  await loadCart()
  ElMessage.success('購物車已刷新')
}

// 清理失效商品
const clearInvalidItems = async () => {
  try {
    await ElMessageBox.confirm(
      `確定要清理 ${invalidItems.value.length} 件失效商品嗎？`,
      '確認清理',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 這裡應該調用後端API清理失效商品
    cartItems.value = cartItems.value.filter(item => !item.invalid)
    ElMessage.success('失效商品已清理')
  } catch {
    // 用戶取消操作
  }
}

// 跳轉到商品詳情
const goToProduct = (productId: number) => {
  router.push(`/products/${productId}`)
}

const toggleSelectAll = () => {
  const allSelected = selectAll.value
  cartItems.value.forEach(item => {
    if (item.selected !== allSelected) {
      toggleItemSelected(item)
    }
  })
}

const toggleItemSelected = async (item: CartItem) => {
  try {
    const response = await fetch(`/api/cart/toggle-selected/${item.id}?selected=${item.selected ? 1 : 0}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (!response.ok) {
      // 如果請求失敗，恢復原狀態
      item.selected = !item.selected
      ElMessage.error('更新選中狀態失敗')
    }
  } catch (error) {
    item.selected = !item.selected
    ElMessage.error('更新選中狀態失敗')
  }
}

const updateQuantity = async (item: CartItem, newQuantity: number) => {
  if (newQuantity < 1) return
  
  const oldQuantity = item.quantity
  item.quantity = newQuantity
  
  try {
    const response = await fetch(`/api/cart/update/${item.id}?quantity=${newQuantity}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (!response.ok) {
      item.quantity = oldQuantity
      ElMessage.error('更新數量失敗')
    }
  } catch (error) {
    item.quantity = oldQuantity
    ElMessage.error('更新數量失敗')
  }
}

const removeItem = async (item: CartItem) => {
  try {
    await ElMessageBox.confirm('確定要刪除這個商品嗎？', '確認刪除', {
      type: 'warning'
    })
    
    const response = await fetch(`/api/cart/remove/${item.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      const index = cartItems.value.findIndex(i => i.id === item.id)
      if (index > -1) {
        cartItems.value.splice(index, 1)
      }
      ElMessage.success('商品已刪除')
    } else {
      ElMessage.error('刪除失敗')
    }
  } catch (error) {
    // 用戶取消刪除
  }
}

const clearCart = async () => {
  try {
    await ElMessageBox.confirm('確定要清空購物車嗎？', '確認清空', {
      type: 'warning'
    })
    
    const response = await fetch('/api/cart/clear', {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      cartItems.value = []
      ElMessage.success('購物車已清空')
    } else {
      ElMessage.error('清空失敗')
    }
  } catch (error) {
    // 用戶取消清空
  }
}

// 移入收藏夾
const moveToFavorites = async (item: CartItem) => {
  try {
    // 這裡應該調用收藏API
    ElMessage.success(`${item.productName} 已移入收藏夾`)
    await removeItem(item)
  } catch (error) {
    ElMessage.error('移入收藏夾失敗')
  }
}

// 找相似商品
const findSimilar = (item: CartItem) => {
  router.push(`/products?similar=${item.productId}`)
}

// 添加推薦商品到購物車
const addRecommendedToCart = async (product: Product) => {
  try {
    const response = await fetch('/api/cart/add', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: new URLSearchParams({
        productId: product.id.toString(),
        quantity: '1'
      })
    })

    const result = await response.json()
    if (result.success) {
      ElMessage.success('已加入購物車')
      await loadCart()
    } else {
      ElMessage.error(result.message || '加入購物車失敗')
    }
  } catch (error) {
    ElMessage.error('加入購物車失敗')
  }
}

// 刷新推薦商品
const refreshRecommendations = async () => {
  await loadRecommendedProducts()
  ElMessage.success('推薦商品已刷新')
}

const goToCheckout = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('請選擇要結算的商品')
    return
  }
  if (hasInvalidItems.value) {
    ElMessage.warning('購物車中有失效商品，請先處理')
    return
  }
  router.push('/checkout')
}

onMounted(() => {
  loadCart()
  loadRecommendedProducts()
})
</script>

<style scoped>
.cart-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.empty-cart {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.cart-content {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.cart-item:hover {
  background-color: #f9f9f9;
}

.cart-item.selected {
  background-color: #f0f9ff;
}

.item-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.item-info {
  flex: 1;
  margin-left: 15px;
}

.item-name {
  font-size: 16px;
  margin-bottom: 8px;
}

.item-price {
  color: #e74c3c;
  font-weight: bold;
}

.item-quantity {
  display: flex;
  align-items: center;
  margin: 0 20px;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
}

.quantity-input {
  width: 60px;
  height: 30px;
  text-align: center;
  border: 1px solid #ddd;
  border-left: none;
  border-right: none;
}

.item-subtotal {
  font-weight: bold;
  color: #e74c3c;
  margin-right: 20px;
}

.cart-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #f9f9f9;
}

.cart-summary {
  display: flex;
  align-items: center;
  gap: 20px;
}

.total-amount {
  font-size: 18px;
  font-weight: bold;
  color: #e74c3c;
}

.cart-actions {
  display: flex;
  gap: 15px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 新增樣式 */
.empty-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin: 20px 0 40px;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.invalid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border-radius: 4px;
}

.item-name:hover {
  color: #409eff;
  cursor: pointer;
}

.spec-tag {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.stock-warning {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}

.cart-recommendations {
  margin-top: 40px;
  background: white;
  border-radius: 12px;
  padding: 24px;
}

.recommendations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.recommendations-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  color: #333;
  margin: 0;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.recommendation-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.recommendation-card:hover {
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .cart-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .cart-footer {
    flex-direction: column;
    gap: 20px;
  }

  .recommendations-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}
</style>
