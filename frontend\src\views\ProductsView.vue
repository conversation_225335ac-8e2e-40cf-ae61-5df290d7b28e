<template>
  <div class="products-view">
    <div class="page-header">
      <h1>{{ pageTitle }}</h1>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首頁</el-breadcrumb-item>
        <el-breadcrumb-item>{{ pageTitle }}</el-breadcrumb-item>
        <el-breadcrumb-item v-if="selectedCategory">{{ selectedCategory.name }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <div class="page-content">
      <div class="sidebar">
        <ProductCategoryTree 
          ref="categoryTreeRef"
          @category-select="handleCategorySelect"
        />
      </div>
      
      <div class="main-content">
        <ProductList 
          ref="productListRef"
          :category-id="selectedCategoryId"
          @product-select="handleProductSelect"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ProductCategoryTree from '../components/ProductCategoryTree.vue'
import ProductList from '../components/ProductList.vue'
import { useProductStore } from '../stores/product'
import type { ProductCategory, Product } from '../api/product'

// Router
const route = useRoute()
const router = useRouter()

// Store
const productStore = useProductStore()

// 組件引用
const categoryTreeRef = ref<InstanceType<typeof ProductCategoryTree>>()
const productListRef = ref<InstanceType<typeof ProductList>>()

// 響應式數據
const selectedCategoryId = ref<number | null>(null)
const selectedCategory = ref<ProductCategory | null>(null)

// 計算頁面標題
const pageTitle = computed(() => {
  const routeName = route.name as string
  switch (routeName) {
    case 'Products':
      return '商品列表'
    case 'ProductCategories':
      return '商品分類'
    case 'ProductSearch':
      return '商品搜索'
    case 'HotProducts':
      return '熱門商品'
    case 'RecommendedProducts':
      return '推薦商品'
    default:
      return '商品列表'
  }
})

// 處理分類選擇
const handleCategorySelect = (categoryId: number | null, category: ProductCategory | null) => {
  selectedCategoryId.value = categoryId
  selectedCategory.value = category
  
  // 更新URL
  if (categoryId) {
    router.push({ query: { category: categoryId.toString() } })
  } else {
    router.push({ query: {} })
  }
}

// 處理商品選擇
const handleProductSelect = (product: Product) => {
  router.push(`/products/${product.id}`)
}

// 從URL獲取分類ID
const getCategoryIdFromUrl = () => {
  const categoryParam = route.query.category
  if (categoryParam && typeof categoryParam === 'string') {
    return parseInt(categoryParam, 10)
  }
  return null
}

// 根據路由設置篩選條件
const setFiltersBasedOnRoute = () => {
  try {
    const routeName = route.name as string
    console.log('ProductsView - setFiltersBasedOnRoute called with route:', routeName)

    // 清除之前的篩選條件
    productStore.clearFilters()

    switch (routeName) {
      case 'HotProducts':
        productStore.toggleHotFilter()
        break
      case 'RecommendedProducts':
        productStore.toggleRecommendedFilter()
        break
      case 'ProductCategories':
      case 'ProductSearch':
      case 'Products':
      default:
        // 默認不設置特殊篩選
        break
    }

    console.log('ProductsView - setFiltersBasedOnRoute completed')
  } catch (error) {
    console.error('ProductsView - setFiltersBasedOnRoute error:', error)
  }
}

// 組件掛載時初始化
onMounted(async () => {
  try {
    console.log('ProductsView - onMounted called, route:', route.name, route.path)

    // 根據路由設置篩選條件
    setFiltersBasedOnRoute()

    // 從URL獲取分類ID
    const categoryId = getCategoryIdFromUrl()
    if (categoryId) {
      selectedCategoryId.value = categoryId

      // 等待分類樹加載完成後選中對應分類
      await nextTick()
      categoryTreeRef.value?.selectCategory(categoryId)
    }

    console.log('ProductsView - onMounted completed successfully')
  } catch (error) {
    console.error('ProductsView - onMounted error:', error)
    // 不要重定向，只記錄錯誤
  }
})

// 監聽路由變化
watch(() => route.name, () => {
  setFiltersBasedOnRoute()
})
</script>

<style scoped>
.products-view {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 12px;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.page-content {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 24px;
}

.sidebar {
  position: sticky;
  top: 20px;
  height: fit-content;
}

@media (max-width: 992px) {
  .page-content {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    position: static;
  }
}
</style>
